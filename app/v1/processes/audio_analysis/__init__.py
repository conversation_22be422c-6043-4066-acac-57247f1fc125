import os
import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any
from bson.objectid import ObjectId
from io import BytesIO

from app.models.user import UserTenantDB
from app.core.helper.logger import setup_new_logging
from app.v1.processes.transcribe_audio import transcribe_single_audio
from app.v1.processes.extract_data_from_audio import extract_audio_info

from app.v1.api.jobs.models import Job, JobStatus

loggers = setup_new_logging(__name__)

class AudioAnalysis:
    def __init__(self, user_tenant_info: UserTenantDB):
        self.user_tenant_info = user_tenant_info
        
    async def process_audio(self, media_id: str) -> Dict[str, Any]:
        """Process an audio file and update job status."""
        media_collection = self.user_tenant_info.async_db.media
        media = await media_collection.find_one({"_id": ObjectId(media_id)})
        
        if not media or not media.get("object_name"):
            raise ValueError(f"Invalid media or missing object name for media {media_id}")
            
        object_name = media["object_name"]
        job_id = media.get("job_id")
        
        try:
            # Fetch Google API key from config collection
            config = await self.user_tenant_info.async_db.config.find_one({"name": "api-keys"})
            if not config or "GOOGLE_API_KEY" not in config.get("value", {}):
                raise ValueError("Google API key not found in configuration")
            
            api_key = config["value"]["GOOGLE_API_KEY"]
            
            # Update status to processing
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {"$set": {"status": "processing"}}
            )
            
            # Get the audio from MinIO
            loop = asyncio.get_event_loop()
            minio_client = self.user_tenant_info.minio_client
            response = await loop.run_in_executor(
                None, 
                minio_client.get_object,
                self.user_tenant_info.minio_bucket_name,
                object_name
            )
            
            # Save to temporary file
            audio_data = await loop.run_in_executor(None, response.read)
            temp_file_path = f"/tmp/{media_id}.mp3"
            with open(temp_file_path, "wb") as f:
                f.write(audio_data)
            
            # Process with both transcription and analysis
            transcription_result = await transcribe_single_audio(temp_file_path, api_key)
            analysis_result = await extract_audio_info(temp_file_path, api_key)
            
            # Clean up temp file
            os.remove(temp_file_path)
            
            # Combine results
            combined_result = {
                "transcription": transcription_result["transcription"],
                "analysis": analysis_result["analysis"],
                "pricing": {
                    "transcription_price_nep": transcription_result["price_nep"],
                    "analysis_price_nep": analysis_result["price_nep"],
                    "total_price_nep": transcription_result["price_nep"] + analysis_result["price_nep"]
                },
                "usage_metadata": {
                    "transcription": transcription_result["usage_metadata"],
                    "analysis": analysis_result["usage_metadata"]
                }
            }
            
            # Save result to MinIO
            result_object_name = f"{job_id}/output/{media_id}_result.json"
            result_bytes = json.dumps(combined_result).encode('utf-8')
            
            await loop.run_in_executor(
                None,
                minio_client.put_object,
                self.user_tenant_info.minio_bucket_name,
                result_object_name,
                BytesIO(result_bytes),
                len(result_bytes),
                'application/json'
            )
            
            # Update media status
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {
                    "$set": {
                        "status": JobStatus.COMPLETED,
                        "completed_at": datetime.utcnow(),
                        "output_object_name": result_object_name
                    }
                }
            )
            
            # Remove this job update as it's now handled in extract_from_job
            # await self.user_tenant_info.async_db.jobs.update_one(
            #     {"_id": ObjectId(job_id), "items.input_id": ObjectId(media_id)},
            #     {"$set": {"items.$.output_id": ObjectId(media_id), "items.$.output_object_name": result_object_name}}
            # )
            
            return combined_result
            
        except Exception as e:
            loggers.error(f"Error processing audio {media_id}: {str(e)}")
            # Update status on failure
            await media_collection.update_one(
                {"_id": ObjectId(media_id)},
                {
                    "$set": {
                        "status": JobStatus.FAILED,
                        "error": str(e),
                        "completed_at": datetime.utcnow()
                    }
                }
            )
            raise Exception(f"Error processing media {media_id}: {str(e)}")

    async def process_multiple_audios(self, media_ids: List[str]) -> List[Dict[str, Any]]:
        """Process multiple audio files in parallel."""
        tasks = [self.process_audio(media_id) for media_id in media_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "media_id": media_ids[i],
                    "error": str(result)
                })
            else:
                processed_results.append({
                    "media_id": media_ids[i],
                    "result": result
                })
        
        return processed_results

    async def extract_from_job(self, job_id: str) -> List[Dict[str, Any]]:
        """Extract data from all audio files in a job in parallel."""
        jobs_collection = self.user_tenant_info.async_db.jobs
        job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
        
        if not job or not job.get("items"):
            raise ValueError(f"Invalid job or no items found for job {job_id}")

        # Update job status to processing
        await jobs_collection.update_one(
            {"_id": ObjectId(job_id)},
            {"$set": {"status": JobStatus.INPROGRESS, "updated_at": datetime.utcnow()}}
        )

        # Get media IDs from items
        media_ids = []
        process_items = {}  # Map to track process items by media_id
        for item in job["items"]:
            if item.get("input_id"):
                media_id = str(item["input_id"])
                media_ids.append(media_id)
                process_items[media_id] = item
        
        try:
            # Process all audio files in parallel
            results = await self.process_multiple_audios(media_ids)
            
            media_collection = self.user_tenant_info.async_db.media

            for result in results:
                media_id = result["media_id"]
                process_item = process_items[media_id]
                
                if "error" in result:
                    # Update process item status to failed
                    process_item["status"] = JobStatus.FAILED
                    process_item["error"] = result["error"]
                else:
                    # Create output media document
                    output_media = {
                        "filename": f"output_{media_id}.json",
                        "content_type": "application/json",
                        "bucket_name": self.user_tenant_info.minio_bucket_name,
                        "object_name": f"{job_id}/output/{media_id}_result.json",
                        "job_id": ObjectId(job_id),
                        "created_at": datetime.utcnow(),
                        "created_by": job["created_by"],
                        "metadata": {
                            "source_media_id": ObjectId(media_id),
                            "process_type": "audio-transcribe-analysis",
                            "type": "output"
                        }
                    }
                    
                    # Insert output media
                    output_result = await media_collection.insert_one(output_media)
                    
                    # Update process item
                    process_item["output_id"] = output_result.inserted_id
                    process_item["output_object_name"] = output_media["object_name"]
                    process_item["status"] = JobStatus.COMPLETED
                    
                    # Upload result to MinIO (already done in process_audio)
                    # We don't need to upload again as it's already done in process_audio
            
            # Update job status and items
            all_successful = all(item.get("status") == JobStatus.COMPLETED for item in process_items.values())
            status = JobStatus.COMPLETED if all_successful else JobStatus.PARTIALLYCOMPLETED
            
            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": status,
                        "completed_at": datetime.utcnow(),
                        "error_count": sum(1 for item in process_items.values() if item.get("status") == JobStatus.FAILED),
                        "items": list(process_items.values())
                    }
                }
            )
            
            return results
        except Exception as e:
            # Update job status to failed
            await jobs_collection.update_one(
                {"_id": ObjectId(job_id)},
                {
                    "$set": {
                        "status": JobStatus.FAILED,
                        "error": str(e),
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            raise e