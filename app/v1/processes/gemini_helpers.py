import asyncio
import google.generativeai as genai

def get_model(api_key: str, model_name: str, generation_config: dict, system_instruction: str = None):
    genai.configure(api_key=api_key)
    return genai.GenerativeModel(
        model_name=model_name,
        generation_config=generation_config,
        system_instruction=system_instruction,
    )

async def async_upload_to_gemini(path, mime_type=None):
    loop = asyncio.get_event_loop()
    file = await loop.run_in_executor(None, lambda: genai.upload_file(path, mime_type=mime_type))
    return file

async def async_generate_content(model, file, prompt: str):
    loop = asyncio.get_event_loop()
    def _generate():
        response = model.generate_content(
            contents=[
                {
                    "role": "user",
                    "parts": [
                        file,
                        prompt,
                    ],
                }
            ]
        )
        return response
    response = await loop.run_in_executor(None, _generate)
    return response

def price_nep(response):
    pricing = {
        "input_price_per_token": 0.75 * 10 ** -6,
        "output_price_per_token": 0.4 * 10 ** -6,
    }
    price_usd = (
        response.usage_metadata.prompt_token_count * pricing["input_price_per_token"]
        + response.usage_metadata.candidates_token_count * pricing["output_price_per_token"]
    )
    price_nep = price_usd * 137
    return price_nep