services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant_image
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - /root/qdrant/data:/qdrant/storage
    labels:
      - "project=qdrant"

  qdrant_backup:
    image: alpine:latest
    container_name: qdrant_backup_image
    restart: unless-stopped
    volumes:
      - /root/qdrant/data:/data:ro
      - /root/qdrant/backups:/backup
      - /var/log/qdrant:/var/log/qdrant
    labels:
      - "project=qdrant"
    entrypoint: >
      sh -c "
        LOG_FILE=/var/log/qdrant/qdrant.log
        BACKUP_FILE=/backup/qdrant_backup.tar.gz
        mkdir -p /var/log/qdrant /backup
        touch $$LOG_FILE
        while true; do
          tar -czf $$BACKUP_FILE -C /data .
          echo [\$$(date)] Backup updated: $$BACKUP_FILE >> $$LOG_FILE
          sleep 3600
        done
      "