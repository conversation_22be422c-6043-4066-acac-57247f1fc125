from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from typing import List, Optional
from app.core.security import require_roles
from app.models.user import UserTenantDB
from app.v1.api.jobs.models import JobStatus
from app.core.helper.logger import setup_new_logging
import json

logger = setup_new_logging(__name__)

router = APIRouter(prefix="/processes", tags=["Processes"])

@router.get("/")
async def list_processes(
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """List all available processes with their descriptions and schemas"""
    processes_ = await user_tenant_info.async_db.config.find_one({"name":"available_processes"})
    PROCESSES = processes_.get("value", {})
    return PROCESSES

@router.get("/{process_name}")
async def get_process(
    process_name: str,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """Get details about a specific process"""

    processes_ = await user_tenant_info.async_db.config.find_one({"name":"available_processes"})
    PROCESSES = processes_.get("value", {})
    if process_name not in PROCESSES:
        raise HTTPException(status_code=404, detail=f"Process '{process_name}' not found")

    return PROCESSES[process_name]

from fastapi import BackgroundTasks
from datetime import datetime, timezone
from bson import ObjectId
import asyncio
import time
from app.v1.api.projects import create_project
from app.v1.api.jobs import create_job, add_media, execute_job
from fastapi import UploadFile

@router.post("/{process_name}/run")
async def run_process(
    process_name: str,
    background_tasks: BackgroundTasks,
    urls: List[str] = [],
    files: List[UploadFile] = [],
    project_name: Optional[str] = None,
    job_name: Optional[str] = None,
    webhook_callback: Optional[str] = None,
    wait_for_completion: bool = True,
    timeout: int = 300,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"])),
):
    """
    Run a complete process on multiple URLs and/or files

    Args:
        process_name: Name of the process to run
        urls: Optional list of URLs to process
        files: Optional list of files to process
        project_name: Optional project name (default is generated)
        job_name: Optional job name (default is generated)
        webhook_callback: Optional webhook URL to call when job is completed
        wait_for_completion: Whether to wait for job completion
        timeout: Maximum time to wait for completion in seconds
    """
    # Split URLs if they contain commas
    expanded_urls = []
    for url in urls:
        if ',' in url:
            expanded_urls.extend([u.strip() for u in url.split(',') if u.strip()])
        else:
            expanded_urls.append(url.strip())

    # Filter out empty URLs and validate the remaining ones
    valid_urls = [url for url in expanded_urls if url]
    if not valid_urls and not files:
        raise HTTPException(status_code=400, detail="At least one valid URL or file must be provided")

    # Validate URLs format
    for url in valid_urls:
        if not url.startswith(('http://', 'https://')):
            raise HTTPException(status_code=400, detail=f"Invalid URL format: {url}. URLs must start with http:// or https://")

    processes_ = await user_tenant_info.async_db.config.find_one({"name":"available_processes"})
    PROCESSES = processes_.get("value", {})
    # Validate process exists
    if process_name not in PROCESSES:
        raise HTTPException(status_code=404, detail=f"Process '{process_name}' not found")

    process_info = PROCESSES[process_name]

    # Set default names if not provided
    if not project_name:
        # Format: "Process Name - YYYY-MM-DD" (just date, no time)
        project_name = f"{process_info['name']} - {datetime.now(timezone.utc).strftime('%Y-%m-%d')}"

    if not job_name:
        # Keep job name as is with date and time
        job_name = f"{process_info['name']} Job - {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S')}"

    # Step 1: Check if project exists, create if it doesn't
    projects_collection = user_tenant_info.async_db.projects
    existing_project = await projects_collection.find_one({"name": project_name})

    # Log webhook_callback status for debugging
    logger.debug(f"Process run request: project_name={project_name}, webhook_callback={webhook_callback}")

    if existing_project:
        project_id = str(existing_project["_id"])
        existing_webhook = existing_project.get("webhook_callback")
        logger.debug(f"Found existing project: id={project_id}, existing_webhook={existing_webhook}")

        # Update webhook_callback based on current request
        if webhook_callback:
            # If webhook_callback is provided in this request, update the project
            if webhook_callback != existing_webhook:
                logger.info(f"Updating project {project_id} webhook_callback from {existing_webhook} to {webhook_callback}")
                await projects_collection.update_one(
                    {"_id": ObjectId(project_id)},
                    {"$set": {"webhook_callback": webhook_callback}}
                )
        else:
            # If no webhook_callback is provided in this request, clear any existing webhook_callback
            if existing_webhook:
                logger.info(f"Clearing webhook_callback for project {project_id} (was {existing_webhook})")
                await projects_collection.update_one(
                    {"_id": ObjectId(project_id)},
                    {"$set": {"webhook_callback": None}}
                )
    else:
        # Use the create_project function from projects API
        from app.v1.api.projects.models import ProjectCreate
        logger.debug(f"Creating new project with name={project_name}, webhook_callback={webhook_callback}")
        project_data = ProjectCreate(
            name=project_name,
            description=f"Auto-created project for {process_name}",
            webhook_callback=webhook_callback
        )
        project = await create_project(project_data, user_tenant_info)
        project_id = str(project["_id"])
        logger.info(f"Created new project: id={project_id}, webhook_callback={webhook_callback}")

    # Step 2: Create job using the jobs API
    from app.v1.api.jobs.models import JobCreate
    job_data = JobCreate(
        name=job_name,
        description=f"Auto-created job for {process_name}",
        process_name=process_name
    )
    job = await create_job(project_id, job_data, user_tenant_info)
    job_id = str(job["_id"])

    # Step 3: Add media from URLs - use the filtered valid_urls instead of urls
    await add_media(job_id, valid_urls, files, user_tenant_info)

    # Step 4: Execute the job - pass the injected background_tasks
    await execute_job(job_id, background_tasks, user_tenant_info)

    # Remove the manual task execution
    # for task in background_tasks.tasks:
    #     await task()

    # Step 5: Wait for completion if requested
    if wait_for_completion:
        jobs_collection = user_tenant_info.async_db.jobs

        # Create a new background tasks object for job processing only
        # This avoids executing the webhook notification tasks directly
        job_processing_tasks = BackgroundTasks()

        # Copy only the job processing task to the new background tasks object
        # This is the first task in the list (the process_job task)
        if background_tasks.tasks:
            job_processing_tasks.add_task(background_tasks.tasks[0])

            # Execute only the job processing task
            for task in job_processing_tasks.tasks:
                await task()

        start_time = time.time()
        while time.time() - start_time < timeout:
            # Check job status
            job = await jobs_collection.find_one({"_id": ObjectId(job_id)})
            if job["status"] in [JobStatus.COMPLETED, JobStatus.FAILED]:
                break
            await asyncio.sleep(2)  # Poll every 2 seconds

        # If we timed out
        if time.time() - start_time >= timeout:
            return {
                "status": JobStatus.TIMEOUT,
                "message": f"Job processing exceeded timeout of {timeout} seconds",
                "project_id": project_id,
                "job_id": job_id
            }

        # Get job output using the output endpoint
        from app.v1.api.jobs import get_job_output
        output_response = await get_job_output(job_id, True, 3600, user_tenant_info)

        # Extract the actual data from the response
        if hasattr(output_response, 'body') and output_response.body:
            # Parse the JSON string in the body
            try:
                results = json.loads(output_response.body)
            except:
                results = {"error": "Failed to parse output response"}
        else:
            results = output_response

        return {
            "status": job["status"],
            "project_id": project_id,
            "job_id": job_id,
            "results": results
        }

    # Return immediately if not waiting
    return {
        "status": JobStatus.INPROGRESS,
        "project_id": project_id,
        "job_id": job_id
    }

@router.post("/generate-schema")
async def generate_schema(
    user_prompt: str = Form(..., description="Description of what schema to generate"),
    url: Optional[str] = Form(None, description="URL to analyze (alternative to file upload)"),
    temperature: float = Form(1.0, ge=0.0, le=2.0, description="Generation temperature"),
    top_p: float = Form(0.95, ge=0.0, le=1.0, description="Top-p parameter"),
    top_k: int = Form(40, ge=1, le=100, description="Top-k parameter"),
    max_output_tokens: int = Form(2048, ge=1, le=8192, description="Max output tokens"),
    file: Optional[UploadFile] = File(None, description="File to analyze (alternative to URL)"),
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    """
    Generate a dynamic JSON schema based on user prompt and content (file or URL).

    This endpoint accepts either a file upload or URL and generates a JSON schema
    using Gemini AI based on the user's description of what they want to extract.

    The generated schema includes:
    - Dynamic response schema based on content analysis
    - System and user prompts generated by Gemini
    - Cost tracking information

    Args:
        user_prompt: Description of what schema to generate
        url: Optional URL to analyze
        temperature: Generation temperature (0.0-2.0)
        top_p: Top-p parameter (0.0-1.0)
        top_k: Top-k parameter (1-100)
        max_output_tokens: Maximum output tokens (1-8192)
        file: Optional file upload to analyze

    Returns:
        JSON schema in the specified format with input, generation_schema, and cost_info
    """
    from .schema_generator import generate_schema_with_gemini
    import mimetypes

    # Validate input - must have either file or URL
    if not file and not url:
        raise HTTPException(
            status_code=400,
            detail="Either 'file' or 'url' must be provided"
        )

    if file and url:
        raise HTTPException(
            status_code=400,
            detail="Provide either 'file' or 'url', not both"
        )

    try:
        # Prepare generation config
        generation_config = {
            "temperature": temperature,
            "top_p": top_p,
            "top_k": top_k,
            "max_output_tokens": max_output_tokens,
            "response_mime_type": "application/json"
        }

        file_content = None
        file_name = None
        mime_type = None

        if file:
            # Handle file upload
            file_content = await file.read()
            file_name = file.filename
            mime_type = file.content_type or mimetypes.guess_type(file.filename)[0] or "application/octet-stream"

            # Validate file size (max 50MB)
            if len(file_content) > 50 * 1024 * 1024:
                raise HTTPException(
                    status_code=400,
                    detail="File size exceeds 50MB limit"
                )

            # Store file in MinIO for processing
            object_name = f"schema_generation/{user_tenant_info.user.id}/{file_name}"

            # Upload to MinIO
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                user_tenant_info.minio_client.put_object,
                user_tenant_info.minio_bucket_name,
                object_name,
                BytesIO(file_content),
                len(file_content),
                mime_type
            )

        elif url:
            # Validate URL format
            if not url.startswith(('http://', 'https://')):
                raise HTTPException(
                    status_code=400,
                    detail="URL must start with http:// or https://"
                )

        # Generate schema using Gemini
        result = await generate_schema_with_gemini(
            user_prompt=user_prompt,
            file_content=file_content,
            file_name=file_name,
            mime_type=mime_type,
            url=url,
            user_tenant_info=user_tenant_info,
            generation_config=generation_config
        )
    
        return result

    except Exception as e:
        logger.error(f"Schema generation failed: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Schema generation failed: {str(e)}"
        )
