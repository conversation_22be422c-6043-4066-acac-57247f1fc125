# app/routers/projects.py

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from typing import List, Optional
from datetime import datetime
from app.core.security import get_tenant_info, require_roles
from bson.objectid import ObjectId
from app.models.user import UserTenantDB
from app.core.helper.mongo_helper import serialize_mongo_doc

from .models import Project, ProjectCreate, ProjectUpdate, ProjectWithStats, JobStatistics
from app.v1.schema.pagination import PaginationResponse

router = APIRouter(tags=["Projects"], prefix="/projects")

@router.post("/create", response_model=Project)
async def create_project(project: ProjectCreate, user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))):

    projects_collection = user_tenant_info.async_db.projects

    new_project = {
        "name": project.name,
        "description": project.description,
        "webhook_callback": str(project.webhook_callback) if project.webhook_callback else None,
        "created_by": ObjectId(user_tenant_info.user.id),
        "created_at": datetime.now(),
    }

    # Check if project with the same name already exists
    existing_project = await projects_collection.find_one({"name": project.name})
    if existing_project:
        raise HTTPException(status_code=400, detail="Project with this name already exists")

    result = await projects_collection.insert_one(new_project)
    new_project["_id"] = result.inserted_id
    return new_project


@router.get("/")
async def get_projects(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    created_by: Optional[str] = None,
    created_at_start: Optional[datetime] = None,
    created_at_end: Optional[datetime] = None,
    search: Optional[str] = None,
    sort_by_created_at: bool = True,
    sort_ascending: bool = False,
    include_job_stats: bool = Query(False, description="Include job statistics for each project"),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info),
) -> PaginationResponse[ProjectWithStats]:
    """
    Get projects with pagination, filtering, and sorting options.

    Parameters:
    - page: Page number (starts at 1)
    - limit: Number of items per page
    - created_by: Filter projects created by this user ID
    - created_at_start: Filter projects created after this datetime
    - created_at_end: Filter projects created before this datetime
    - search: Search string for project name, description, and job names/descriptions
    - sort_by_created_at: Whether to sort by created_at field
    - sort_ascending: Sort in ascending order if True, descending if False

    - include_job_stats: Include job statistics (counts by status and process type) for each project

    When include_job_stats=True, each project will include:
    - job_statistics.total_jobs: Total number of jobs in the project
    - job_statistics.by_status: Job counts grouped by status (pending, completed, etc.)
    - job_statistics.by_process_type: Job counts grouped by process type (extract-image-data, etc.)
    """ 
    projects_collection = user_tenant_info.async_db.projects

    # Build the aggregation pipeline
    pipeline = []

    # Match stage for filtering
    match_query = {}

    if created_by:
        if not ObjectId.is_valid(created_by):
            raise HTTPException(status_code=400, detail="Invalid created_by user ID")
        match_query["created_by"] = ObjectId(created_by)

    if created_at_start or created_at_end:
        match_query["created_at"] = {}
        if created_at_start:
            match_query["created_at"]["$gte"] = created_at_start
        if created_at_end:
            match_query["created_at"]["$lte"] = created_at_end

    # Add search functionality for project name and description
    if search:
        match_query["$or"] = [
            {"name": {"$regex": search, "$options": "i"}},
            {"description": {"$regex": search, "$options": "i"}}
        ]

    if match_query:
        pipeline.append({"$match": match_query})

    # Sort stage
    if sort_by_created_at:
        sort_direction = 1 if sort_ascending else -1
        pipeline.append({"$sort": {"created_at": sort_direction}})

    # Count total documents for pagination
    count_pipeline = pipeline.copy()
    count_pipeline.append({"$count": "total"})

    # Get total count
    cursor = await projects_collection.aggregate(count_pipeline)
    count_result = await cursor.to_list(length=1)
    total = count_result[0]["total"] if count_result else 0

    # Add pagination stages
    skip = (page - 1) * limit
    pipeline.append({"$skip": skip})
    pipeline.append({"$limit": limit})

    # Add job statistics aggregation if requested
    if include_job_stats:
        # Add lookup and job statistics calculation stages
        pipeline.extend([
            {
                "$lookup": {
                    "from": "jobs",
                    "localField": "_id",
                    "foreignField": "project_id",
                    "as": "jobs"
                }
            },
            {
                "$addFields": {
                    "job_statistics": {
                        "total_jobs": {"$size": "$jobs"},
                        "by_status": {
                            "pending": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "pending"]}
                                    }
                                }
                            },
                            "in-progress": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "in-progress"]}
                                    }
                                }
                            },
                            "completed": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "completed"]}
                                    }
                                }
                            },
                            "failed": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "failed"]}
                                    }
                                }
                            },
                            "timeout": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "timeout"]}
                                    }
                                }
                            },
                            "cancelled": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "cancelled"]}
                                    }
                                }
                            },
                            "partially-completed": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "partially-completed"]}
                                    }
                                }
                            }
                        },
                        "by_process_type": {
                            "extract-image-data": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.process_name", "extract-image-data"]}
                                    }
                                }
                            },
                            "audio-transcribe-analysis": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.process_name", "audio-transcribe-analysis"]}
                                    }
                                }
                            },
                            "generic-entity-extraction": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.process_name", "generic-entity-extraction"]}
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                "$project": {
                    "jobs": 0  # Remove the jobs array from the output
                }
            }
        ])

    # Execute the aggregation
    cursor = await projects_collection.aggregate(pipeline)
    projects = await cursor.to_list(length=None)

    # If job stats not requested, add empty job_statistics to match the response model
    if not include_job_stats:
        for project in projects:
            project["job_statistics"] = {
                "total_jobs": 0,
                "by_status": {
                    "pending": 0,
                    "in-progress": 0,
                    "completed": 0,
                    "failed": 0,
                    "timeout": 0,
                    "cancelled": 0,
                    "partially-completed": 0
                },
                "by_process_type": {
                    "extract-image-data": 0,
                    "audio-transcribe-analysis": 0,
                    "generic-entity-extraction": 0
                }
            }

    return {
        "data": projects,
        "meta": {
            "page": page,
            "limit": limit,
            "total": total,
            "total_pages": (total + limit - 1) // limit,
        },
    }

@router.get("/{project_id}", response_model=ProjectWithStats)
async def get_project_details(
    project_id: str,
    include_job_stats: bool = Query(True, description="Include job statistics for the project"),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info)
    ):
    projects_collection = user_tenant_info.async_db.projects
    jobs_collection = user_tenant_info.async_db.jobs

    if not ObjectId.is_valid(project_id):
        raise HTTPException(status_code=400, detail="Invalid project ID")

    project = await projects_collection.find_one({"_id": ObjectId(project_id)})
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    if include_job_stats:
        # Get job statistics using aggregation
        pipeline = [
            {"$match": {"_id": ObjectId(project_id)}},
            {
                "$lookup": {
                    "from": "jobs",
                    "localField": "_id",
                    "foreignField": "project_id",
                    "as": "jobs"
                }
            },
            {
                "$addFields": {
                    "job_statistics": {
                        "total_jobs": {"$size": "$jobs"},
                        "by_status": {
                            "pending": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "pending"]}
                                    }
                                }
                            },
                            "in-progress": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "in-progress"]}
                                    }
                                }
                            },
                            "completed": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "completed"]}
                                    }
                                }
                            },
                            "failed": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "failed"]}
                                    }
                                }
                            },
                            "timeout": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "timeout"]}
                                    }
                                }
                            },
                            "cancelled": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "cancelled"]}
                                    }
                                }
                            },
                            "partially-completed": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.status", "partially-completed"]}
                                    }
                                }
                            }
                        },
                        "by_process_type": {
                            "extract-image-data": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.process_name", "extract-image-data"]}
                                    }
                                }
                            },
                            "audio-transcribe-analysis": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.process_name", "audio-transcribe-analysis"]}
                                    }
                                }
                            },
                            "generic-entity-extraction": {
                                "$size": {
                                    "$filter": {
                                        "input": "$jobs",
                                        "cond": {"$eq": ["$$this.process_name", "generic-entity-extraction"]}
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                "$project": {
                    "jobs": 0
                }
            }
        ]

        cursor = await projects_collection.aggregate(pipeline)
        result = await cursor.to_list(length=1)
        if result:
            project = result[0]
        else:
            # Fallback if aggregation fails
            project["job_statistics"] = {
                "total_jobs": 0,
                "by_status": {
                    "pending": 0,
                    "in-progress": 0,
                    "completed": 0,
                    "failed": 0,
                    "timeout": 0,
                    "cancelled": 0,
                    "partially-completed": 0
                },
                "by_process_type": {
                    "extract-image-data": 0,
                    "audio-transcribe-analysis": 0,
                    "generic-entity-extraction": 0
                }
            }
    else:
        # Add empty job statistics
        project["job_statistics"] = {
            "total_jobs": 0,
            "by_status": {
                "pending": 0,
                "in-progress": 0,
                "completed": 0,
                "failed": 0,
                "timeout": 0,
                "cancelled": 0,
                "partially-completed": 0
            },
            "by_process_type": {
                "extract-image-data": 0,
                "audio-transcribe-analysis": 0,
                "generic-entity-extraction": 0
            }
        }

    return project

@router.delete("/{project_id}")
async def delete_project(project_id: str, user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))):
    """
    Delete a project and all its associated jobs and media files.
    This operation cascades to delete:
    1. All jobs belonging to the project
    2. All media files associated with those jobs (from both MongoDB and MinIO)
    3. The project itself
    """
    projects_collection = user_tenant_info.async_db.projects
    jobs_collection = user_tenant_info.async_db.jobs
    media_collection = user_tenant_info.async_db.media

    # Verify project exists
    if not ObjectId.is_valid(project_id):
        raise HTTPException(status_code=400, detail="Invalid project ID")

    project = await projects_collection.find_one({"_id": ObjectId(project_id)})
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    result = await projects_collection.delete_one({"_id": ObjectId(project_id)})
    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Project not found")

    return JSONResponse(status_code=200, content={"detail": "Project deleted successfully"})

@router.put("/{project_id}", response_model=Project)
async def update_project(
    project_id: str,
    project: ProjectUpdate,
    user_tenant_info: UserTenantDB = Depends(require_roles(["admin"]))
):
    projects_collection = user_tenant_info.async_db.projects

    update_data = {k: v for k, v in project.model_dump().items() if v is not None}
    update_data["updated_at"] = datetime.now()

    result = await projects_collection.update_one(
        {"_id": ObjectId(project_id)},
        {"$set": update_data}
    )

    if result.modified_count == 0:
        raise HTTPException(status_code=404, detail="Project not found or no changes made")

    updated_project = await projects_collection.find_one({"_id": ObjectId(project_id)})
    return updated_project


@router.get("/{project_id}/jobs")
async def get_project_jobs(
    project_id: str,
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    created_at_start: Optional[datetime] = None,
    created_at_end: Optional[datetime] = None,
    created_by: Optional[str] = None,
    search: Optional[str] = None,
    status: Optional[str] = None,
    sort_by_created_at: bool = Query(True, description="Sort by created_at field"),
    sort_ascending: bool = Query(False, description="Sort in ascending order (True) or descending order (False)"),
    projection: List[str] = Query(["_id", "name", "description"], description="Fields to include in the response"),
    user_tenant_info: UserTenantDB = Depends(get_tenant_info),
) -> PaginationResponse[dict]:
    """
    Get all jobs for a specific project with pagination, filtering, and projection.

    Parameters:
    - project_id: ID of the project
    - page: Page number (starts at 1)
    - limit: Number of items per page
    - created_at_start: Filter jobs created after this datetime
    - created_at_end: Filter jobs created before this datetime
    - created_by: Filter jobs created by this user ID
    - search: Search string for job name and description
    - status: Filter jobs by status
    - sort_by_created_at: Sort by created_at field (default: True)
    - sort_ascending: Sort in ascending order (True) or descending order (False, default)
    - projection: Fields to include in the response
    """
    projects_collection = user_tenant_info.async_db.projects
    jobs_collection = user_tenant_info.async_db.jobs

    # Verify project exists
    if not ObjectId.is_valid(project_id):
        raise HTTPException(status_code=400, detail="Invalid project ID")
    project = await projects_collection.find_one({"_id": ObjectId(project_id)})
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")

    # Build filter query
    query = {"project_id": ObjectId(project_id)}

    if created_at_start:
        query["created_at"] = {"$gte": created_at_start}

    if created_at_end:
        # Add 23:59:59.999 to the end date to include the entire day
        end_of_day = created_at_end.replace(hour=23, minute=59, second=59, microsecond=999999)
        if "created_at" in query:
            query["created_at"]["$lte"] = end_of_day
        else:
            query["created_at"] = {"$lte": created_at_end}

    if created_by:
        if not ObjectId.is_valid(created_by):
            raise HTTPException(status_code=400, detail="Invalid created_by user ID")
        query["created_by"] = ObjectId(created_by)

    if status:
        query["status"] = status

    if search:
        query["$or"] = [
            {"name": {"$regex": search, "$options": "i"}},
            {"description": {"$regex": search, "$options": "i"}}
        ]

    # Build projection
    proj = {field: 1 for field in projection}

    # Calculate pagination
    skip = (page - 1) * limit

    # Get total count
    total = await jobs_collection.count_documents(query)

    # Get paginated data with projection
    jobs = await jobs_collection.find(query, proj).skip(skip).limit(limit).to_list(length=None)

    # Convert ObjectIds to strings and datetimes to isoformat

    return {
        "data": serialize_mongo_doc(jobs),
        "meta": {
            "page": page,
            "limit": limit,
            "total": total,
            "total_pages": (total + limit - 1) // limit,
        },
    }
