import os
import asyncio
import google.generativeai as genai
from typing import List
import json

generation_config = {
    "temperature": 1,
    "top_p": 0.95,
    "top_k": 40,
    "max_output_tokens": 8192,
    "response_mime_type": "application/json",
    "response_schema": {
        "type": "array",
        "description": "A list of utterances from a conversation.",
        "items": {
            "type": "object",
            "properties": {
                "sender": {
                    "type": "string",
                    "description": "The speaker of the utterance.",
                    "enum": ["customer", "agent"]
                },
                "utterance": {
                    "type": "string",
                    "description": "The exact text of the utterance (identical to response)."
                },
                "timestamp": {
                    "type": "string",
                    "description": "The time of the utterance in seconds."
                }
            },
            "required": ["sender", "utterance", "timestamp"]
        }
    }
}

def get_model(api_key: str):
    genai.configure(api_key=api_key)
    return genai.GenerativeModel(
        model_name="gemini-2.0-flash-lite",
        generation_config=generation_config,
    )

async def async_upload_to_gemini(path, mime_type=None):
    loop = asyncio.get_event_loop()
    file = await loop.run_in_executor(None, genai.upload_file, path)
    return file

async def async_transcribe_audio(model, file, prompt: str):
    loop = asyncio.get_event_loop()
    def _transcribe():
        response = model.generate_content(
            contents=[
                {
                    "role": "user",
                    "parts": [
                        file,
                        prompt,
                    ],
                }
            ]
        )
        return response
    response = await loop.run_in_executor(None, _transcribe)
    return response

def price_nep(response):
    pricing = {
        "input_price_per_token": 0.75 * 10 ** -6,
        "output_price_per_token": 0.4 * 10 ** -6,
    }
    price_usd = (
        response.usage_metadata.prompt_token_count * pricing["input_price_per_token"]
        + response.usage_metadata.candidates_token_count * pricing["output_price_per_token"]
    )
    price_nep = price_usd * 137
    return price_nep

async def transcribe_single_audio(audio_path: str, api_key: str, prompt: str = None):
    if prompt is None:
        prompt = ("""
        You are a professional audio transcription assistant specializing in conversation analysis.
        Your task is to transcribe the uploaded audio conversation in its original language with high accuracy. For each speaker turn, extract and format the following information:
        - "speaker": Clearly identify the speaker as either "Customer" or "Agent"
        - "content": Provide the verbatim transcription of what was said, including filler words and hesitations
        - "timestamp": Note the starting time of each utterance in the MM:SS format.
        """
        )
    model = get_model(api_key)
    file = await async_upload_to_gemini(audio_path, mime_type="audio/mpeg")
    response = await async_transcribe_audio(model, file, prompt)
    try:
        transcription_json = json.loads(response.text)
    except json.JSONDecodeError:
        transcription_json = []
    return {
        "transcription": transcription_json,
        "price_nep": price_nep(response),
        "usage_metadata": {
            "prompt_token_count": response.usage_metadata.prompt_token_count,
            "candidates_token_count": response.usage_metadata.candidates_token_count,
        }
    }

async def batch_transcribe_audios(audio_paths: List[str], api_key: str, prompt: str = None):
    tasks = [transcribe_single_audio(path, api_key, prompt) for path in audio_paths]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    return results

# Example usage for a single file:
# result = asyncio.run(transcribe_single_audio("/path/to/audio.mp3", api_key="YOUR_API_KEY"))
# print(result)

# Example usage for batch:
# audio_files = ["/path/to/audio1.mp3", "/path/to/audio2.mp3"]
# results = asyncio.run(batch_transcribe_audios(audio_files, api_key="YOUR_API_KEY"))
# print(results)

