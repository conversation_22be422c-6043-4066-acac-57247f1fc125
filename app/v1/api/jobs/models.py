"""
Create Job
name
description optional
process_name
"""

from pydantic import BaseModel, ConfigDict, Field, PlainSerializer, field_validator, model_validator
from typing import Annotated, Optional, Any, List, Dict
from datetime import datetime
from bson import ObjectId
from enum import Enum



class JobStatus(str, Enum):
    PENDING = "pending"
    INPROGRESS = "in-progress"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    PARTIALLYCOMPLETED = "partially-completed"

    def __str__(self):
        return self.value


class ProcessItem(BaseModel):
    input_id: Annotated[ObjectId,PlainSerializer(str)] 
    output_id: Annotated[ObjectId,PlainSerializer(str)]  | None
    status: JobStatus

    def to_dict(self):
        return {
            "input_id": self.input_id,  
            "output_id": self.output_id,
            "status": self.status.value
        }

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )

class JobBase(BaseModel):
    name: str = Field(..., title="Job name", description="Name of the job")
    description: Optional[str] = Field(None, title="Job description", description="Description of the job")
    process_name: str = Field(..., title="Process name", description="Name of the process to be executed")

class JobCreate(BaseModel):
    name: str
    description: Optional[str] = None
    process_name: str


class JobUpdate(BaseModel):
    name: Optional[str] = Field(None, title="Job name", description="Name of the job")
    description: Optional[str] = Field(None, title="Job description", description="Description of the job")

    @field_validator("name")
    def validate_name(cls, value):
        if value is not None and len(value) < 3:
            raise ValueError("Job name must be at least 3 characters long")
        return value

    @field_validator("description")
    def validate_description(cls, value):
        if value is not None and len(value) > 500:
            raise ValueError("Job description must be less than 500 characters")
        return value


    @model_validator(mode="after")
    def check_at_least_one_field(cls, values):
        if values.name is None and values.description is None:
            raise ValueError("At least one field (name or description) must be provided")
        return values
    

class Job(JobBase):
    id: Annotated[ObjectId,PlainSerializer(str)] = Field(alias="_id", title="Job ID", description="Unique identifier for the job")
    created_at: datetime = Field(..., title="Created at", description="Datetime when the job was created")
    updated_at: Optional[datetime] = Field(None, title="Updated at", description="Datetime when the job was last updated")
    created_by: Annotated[ObjectId,PlainSerializer(str)] = Field(..., title="Created by", description="ID of the user who created the job")
    project_id: Annotated[ObjectId,PlainSerializer(str)] = Field(..., title="Project ID", description="ID of the project the job belongs to")
    status: JobStatus = Field(..., title="Job status", description="Current status of the job")
    assigned_to: Optional[Annotated[ObjectId,PlainSerializer(str)]] = Field(None, title="Assigned to", description="ID of the user assigned to this job")
    assigned_at: Optional[datetime] = Field(None, title="Assigned at", description="Datetime when the job was assigned")
    assigned_by: Optional[Annotated[ObjectId,PlainSerializer(str)]] = Field(None, title="Assigned by", description="ID of the user who assigned the job")

    items: List[ProcessItem]


    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


# Assignment-related models
class JobAssignment(BaseModel):
    job_ids: List[str] = Field(..., title="Job IDs", description="List of job IDs to assign")
    user_id: str = Field(..., title="User ID", description="ID of the user to assign jobs to")

    @field_validator("job_ids")
    def validate_job_ids(cls, value):
        if not value:
            raise ValueError("At least one job ID must be provided")
        for job_id in value:
            if not ObjectId.is_valid(job_id):
                raise ValueError(f"Invalid job ID: {job_id}")
        return value

    @field_validator("user_id")
    def validate_user_id(cls, value):
        if not ObjectId.is_valid(value):
            raise ValueError("Invalid user ID")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class JobUnassignment(BaseModel):
    job_ids: List[str] = Field(..., title="Job IDs", description="List of job IDs to unassign")

    @field_validator("job_ids")
    def validate_job_ids(cls, value):
        if not value:
            raise ValueError("At least one job ID must be provided")
        for job_id in value:
            if not ObjectId.is_valid(job_id):
                raise ValueError(f"Invalid job ID: {job_id}")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class JobWithAssignmentInfo(BaseModel):
    """Job model with assignment and project information for listing"""
    id: Annotated[ObjectId,PlainSerializer(str)] = Field(alias="_id")
    name: str
    description: Optional[str] = None
    status: JobStatus
    created_at: datetime
    assigned_to: Optional[Annotated[ObjectId,PlainSerializer(str)]] = None
    assigned_to_name: Optional[str] = None
    project_id: Annotated[ObjectId,PlainSerializer(str)] = Field(..., title="Project ID", description="ID of the project the job belongs to")
    project_name: str
    process_name: Optional[str] = Field(None, title="Process name", description="Name of the process used for the job")

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class OutputVerification(BaseModel):
    """Model for output verification - only includes fields that can be set by the client"""
    verified: bool = Field(..., title="Verified", description="Whether the output is verified")
    verification_notes: Optional[str] = Field(None, title="Verification notes", description="Notes about the verification")

    # Note: verified_by and verified_at are set automatically by the server

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class OutputEdit(BaseModel):
    """Model for editing job output - expects data wrapped in {result} structure and """
    result: Dict[str, Any] = Field(..., title="Result data", description="Updated result data wrapped in result field")
    @field_validator("result")
    def validate_result_data(cls, value):
        if not value:
            raise ValueError("Result data cannot be empty")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )


class VerificationCount(BaseModel):
    """Model for verification count response"""
    verified: int = Field(..., title="Verified count", description="Number of verified outputs")
    unverified: int = Field(..., title="Unverified count", description="Number of unverified outputs")

    model_config = ConfigDict(
        populate_by_name=True,
        arbitrary_types_allowed=True,
    )
